<kendo-tabstrip
  #tabstrip
  (tabSelect)="onSelect($event)"
  [ngStyle]="{ opacity: currentUser() ? '1' : '0' }"
  class="v-custom-tabstrip v-custom-tabstrip-case">
  <kendo-tabstrip-tab
    #caseTab
    *ngIf="!isReviewer()"
    cssClass="t-relative t-h-[inherit]"
    [title]="launchpadTabTypes.CASE"
    [selected]="!isReviewer()">
    <ng-template kendoTabContent>
      <div
        class="t-flex t-flex-col t-relative t-max-h-[calc(100vh_-_51px)] t-grow t-border-l-[1px] t-border-l-[#ccc]">
        @defer{ @if (isMediaStatusLoading()) {
        <div
          class="k-i-loading t-absolute t-h-full t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-top-0 t-left-0 t-right-0 t-bottom-0 t-text-[58px] t-z-10"></div>
        }
        <venio-launchpad-toolbar
          (toolbarActionClick)="caseToolbarActionClick($event)" />
        <venio-launchpad-sub-toolbar
          [isLoading]="isCaseLoading()"
          (pagingChange)="pagingChanged($event, 'CASE_PAGING')"
          (searchChange)="searchControlChanged($event, 'CASE_SEARCH')" />
        <venio-case-grid (actionInvoked)="caseGridActionClick($event)" />
        } @placeholder {
        <div class="t-flex t-grow t-mt-4 t-gap-3">
          @for(n of [1,2,3,4,5]; track n){
          <kendo-skeleton class="t-w-1/5" height="30px" shape="rectangle" />
          }
        </div>
        }
      </div>
    </ng-template>
  </kendo-tabstrip-tab>
  @if(canManageReviewSet() || anyReviewSet()){
  <kendo-tabstrip-tab
    #reviewTab
    [selected]="isReviewer()"
    [title]="launchpadTabTypes.REVIEW_SET"
    cssClass="t-relative t-h-[inherit]">
    <ng-template kendoTabContent>
      @defer{
      <venio-reviewset-container />
      } @placeholder {
      <div class="t-flex t-grow t-mt-4 t-gap-3">
        @for(n of [1,2,3,4,5]; track n){
        <kendo-skeleton class="t-w-1/5" height="30px" shape="rectangle" />
        }
      </div>
      }
    </ng-template>
  </kendo-tabstrip-tab>
  } @if(canViewSharedDoc() && documentShareAvailable()){
  <kendo-tabstrip-tab
    cssClass="t-relative t-h-[inherit]"
    [title]="launchpadTabTypes.SHARED_DOCUMENT">
    <ng-template kendoTabContent>
      @defer{
      <venio-shared-documents-container />
      } @placeholder {
      <div class="t-flex t-grow t-gap-3 t-mt-4 t-p-4">
        @for(n of [1,2,3,4,5]; track n){
        <kendo-skeleton class="t-w-1/5" height="30px" shape="rectangle" />
        }
      </div>
      }
    </ng-template> </kendo-tabstrip-tab
  >}
</kendo-tabstrip>
<div kendoWindowContainer></div>

<ng-template #windowTitleBar let-win>
  <span class="k-window-title t-items-center t-text-primary t-text-lg">
    <div
      class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-mr-1 t-flex t-items-center t-justify-center t-rounded-full">
      <img
        src="assets/svg/icon-case-branch-tree.svg"
        alt="Share Icon"
        style="width: 20px; height: 20px" />
    </div>
    {{ windowTitle() }}
  </span>
  <button
    kendoWindowCloseAction
    [window]="win"
    class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
</ng-template>
