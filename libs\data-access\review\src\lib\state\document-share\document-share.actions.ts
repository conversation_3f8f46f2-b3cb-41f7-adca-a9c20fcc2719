import { createAction, props } from '@ngrx/store'
import {
  DocumentShareModel,
  DocumentShareUserModel,
  SharedDocRequestType,
  SharedDocumentDetailModel,
  SharedDocumentDetailRequestInfo,
} from '../../models/interfaces/document-share.model'
import { UserMessageModel } from '../../models/interfaces/common.models'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { DocumentShareState } from './document-share.reducer'
import { HttpErrorResponse } from '@angular/common/http'

export enum SharedDocumentActionTypes {
  // Actions for Fetch Shared Documents
  FetchSharedDocumentAvailability = '[Shared Document] Fetch Shared Document Availability',
  SetSharedDocumentAvailability = '[Shared Document] Set Shared Document Availability',
  FetchSharedDocuments = '[Shared Document] Fetch Shared Documents',
  FetchSharedDocumentsSuccess = '[Shared Document] Fetch Shared Documents: Success',
  FetchSharedDocumentsFailure = '[Shared Document] Fetch Shared Documents: Failure',

  // Actions for Fetch Shared Document Detail
  FetchSharedDocumentDetail = '[Shared Document] Fetch Shared Document Detail',
  FetchSharedDocumentDetailSuccess = '[Shared Document] Fetch Shared Document Detail: Success',
  FetchSharedDocumentDetailFailure = '[Shared Document] Fetch Shared Document Detail: Failure',

  // Actions for Shared Document Request Update
  SharedDocumentRequestUpdate = '[Shared Document] Shared Document Request Update',

  FetchSharedDocsCount = '[Shared Document] Fetch Shared Documents Count',
  FetchSharedDocsCountSuccess = '[Shared Document] Fetch Shared Documents Count Success',

  // Resetting Shared Document State
  ResetSharedDocumentState = '[Shared Document] Reset State',

  // Actions for Updating Expiry Date
  UpdateExpiryDate = '[Shared Document] Update Expiry Date',
  UpdateExpiryDateSuccess = '[Shared Document] Update Expiry Date: Success',
  UpdateExpiryDateFailure = '[Shared Document] Update Expiry Date: Failure',

  // Actions for Unsharing Document
  UnShareDocument = '[Shared Document] UnShare Document',
  UnShareDocumentSuccess = '[Shared Document] UnShare Document: Success',
  UnShareDocumentFailure = '[Shared Document] UnShare Document: Failure',
}

export const fetchDocumentShareUsersInternal = createAction(
  '[Document Share] Fetch Document Share Users Internal',
  props<{ projectId: number }>()
)

export const setDocumentShareUsersInternal = createAction(
  '[Document Share] Set Document Share Users Internal',
  props<{ userModels: DocumentShareUserModel[] }>()
)

export const fetchDocumentShareUsersExternal = createAction(
  '[Document Share] Fetch Document Share Users External',
  props<{ projectId: number }>()
)

export const setDocumentShareUsersExternal = createAction(
  '[Document Share] Set Document Share Users External',
  props<{ userModels: DocumentShareUserModel[] }>()
)

export const shareDocuments = createAction(
  '[Document Share] Share Documents',
  props<{ projectId: number; requestModel: DocumentShareModel }>()
)

export const setUserMessage = createAction(
  '[Document Share] Set Response Message',
  props<{ messageModel: UserMessageModel }>()
)

export const setInvitationInProgressFlag = createAction(
  '[Document Share] Set Invitation In Progress Flag',
  props<{ isInvitationInProgress: boolean }>()
)

export const resetDocumentShareState = createAction(
  '[Document Share] Reset Document Share State'
)

export const resetSharedDocumentState = createAction(
  SharedDocumentActionTypes.ResetSharedDocumentState,
  props<{
    stateKey: keyof DocumentShareState | Array<keyof DocumentShareState>
  }>()
)

export const fetchSharedDocumentAvailability = createAction(
  SharedDocumentActionTypes.FetchSharedDocumentAvailability,
  props<{ projectId: number }>()
)

export const setSharedDocumentAvailability = createAction(
  SharedDocumentActionTypes.SetSharedDocumentAvailability,
  props<{ documentShareAvailable: boolean }>()
)

export const fetchSharedDocuments = createAction(
  SharedDocumentActionTypes.FetchSharedDocuments,
  props<{ projectId: number; filterQuery: SharedDocRequestType }>()
)

export const fetchSharedDocumentsFailure = createAction(
  SharedDocumentActionTypes.FetchSharedDocumentsFailure,
  props<{ sharedDocumentsErrorResponse: HttpErrorResponse }>()
)

export const fetchSharedDocumentsSuccess = createAction(
  SharedDocumentActionTypes.FetchSharedDocumentsSuccess,
  props<{ sharedDocumentsSuccessResponse: SharedDocumentDetailModel[] }>()
)

export const fetchSharedDocumentDetail = createAction(
  SharedDocumentActionTypes.FetchSharedDocumentDetail,
  props<{ projectId: number; docShareId: number }>()
)

export const fetchSharedDocumentDetailFailure = createAction(
  SharedDocumentActionTypes.FetchSharedDocumentDetailFailure,
  props<{ sharedDocumentDetailErrorResponse: HttpErrorResponse }>()
)

export const fetchSharedDocumentDetailSuccess = createAction(
  SharedDocumentActionTypes.FetchSharedDocumentDetailSuccess,
  props<{ sharedDocumentDetailSuccessResponse: ResponseModel }>()
)

export const updateSharedDocumentRequestInfo = createAction(
  SharedDocumentActionTypes.SharedDocumentRequestUpdate,
  props<{
    sharedDocumentRequestInfo: Partial<SharedDocumentDetailRequestInfo>
  }>()
)

export const fetchSharedDocumentCountDetail = createAction(
  SharedDocumentActionTypes.FetchSharedDocsCount
)

export const fetchSharedDocumentCountDetailSuccess = createAction(
  SharedDocumentActionTypes.FetchSharedDocsCountSuccess,
  props<{ sharedDocumentCountDetailSuccessResponse: ResponseModel }>()
)

export const fetchSharedDocumentCountDetailError = createAction(
  SharedDocumentActionTypes.FetchSharedDocsCountSuccess,
  props<{ sharedDocumentCountDetailErrorResponse: ResponseModel }>()
)

export const updateExpiryDate = createAction(
  SharedDocumentActionTypes.UpdateExpiryDate,
  props<{
    projectId: number
    docShareId: number
    payload: SharedDocumentDetailModel
  }>()
)

export const updateExpiryDateSuccess = createAction(
  SharedDocumentActionTypes.UpdateExpiryDateSuccess,
  props<{ updatedDocumentResponse: ResponseModel }>()
)

export const updateExpiryDateFailure = createAction(
  SharedDocumentActionTypes.UpdateExpiryDateFailure,
  props<{ errorResponse: HttpErrorResponse }>()
)

export const unShareDocument = createAction(
  SharedDocumentActionTypes.UnShareDocument,
  props<{
    projectId: number
    docShareId: number
    payload: SharedDocumentDetailModel
  }>()
)

export const unShareDocumentSuccess = createAction(
  SharedDocumentActionTypes.UnShareDocumentSuccess,
  props<{ unsharedDocumentResponse: ResponseModel }>()
)

export const unShareDocumentFailure = createAction(
  SharedDocumentActionTypes.UnShareDocumentFailure,
  props<{ errorResponse: HttpErrorResponse }>()
)
