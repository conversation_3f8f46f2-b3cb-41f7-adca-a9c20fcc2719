import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@venio/shared/environments'
import {
  SharedDocRequestType,
  SharedDocumentDetailModel,
  SharedDocumentDetailRequestInfo,
} from '../models/interfaces/document-share.model'
import { Observable } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable({
  providedIn: 'root',
})
export class DocumentShareService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchDocumentShareDetailIdWise = <T>(
    projectId: number,
    documentShareId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}documentshare/project/${projectId}/documentShareId/${documentShareId}`
    )

  public updateDocumentShareExpiryDate(
    projectId: number,
    documentShareId: number,
    payload: SharedDocumentDetailModel
  ): Observable<ResponseModel> {
    return this.http.put<ResponseModel>(
      `${this._apiUrl}documentshare/project/${projectId}/documentShareId/${documentShareId}`,
      payload
    )
  }

  public unshareDocumentShare(
    projectId: number,
    documentShareId: number,
    payload: SharedDocumentDetailModel
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}documentshare/project/${projectId}/documentShareId/${documentShareId}`,
      payload
    )
  }

  public fetchDocumentShareUsersInternal = <T>(
    projectId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}documentshare/project/${projectId}/users?userType=INTERNAL`
    )

  public fetchDocumentShareUsersExternal = <T>(
    projectId: number
  ): Observable<T> =>
    this.http.get<T>(
      `${this._apiUrl}documentshare/project/${projectId}/users?userType=EXTERNAL`
    )

  public shareDocument = <T>(
    projectId: number,
    shareDocumentModel: any
  ): Observable<T> =>
    this.http.post<T>(
      `${this._apiUrl}documentshare/project/${projectId}`,
      shareDocumentModel
    )

  public fetchSharedDocList(
    projectId: number,
    filterQuery: SharedDocRequestType,
    queryParams: SharedDocumentDetailRequestInfo
  ): Observable<SharedDocumentDetailModel[]> {
    const searchFilter = queryParams?.searchText?.trim()
      ? queryParams.searchText
      : ''

    const params = new HttpParams()
      .set('ProjectId', projectId.toString())
      .set('filterQuery', filterQuery as unknown as string)
      .set('searchFilter', searchFilter)

    return this.http.get<SharedDocumentDetailModel[]>(
      `${this._apiUrl}documentshare`,
      { params }
    )
  }

  public fetchSharedDocDetail(
    projectId: number,
    docShareId: number
  ): Observable<any> {
    const docUrl = `${this._apiUrl}documentshare/project/${projectId}/documentShareId/${docShareId}`
    return this.http.get(docUrl)
  }

  public fetchSharedDocumentAvailability(projectId: number): Observable<any> {
    const docUrl = `${this._apiUrl}documentshare/project/${projectId}/HasDocumentShares`
    return this.http.get(docUrl)
  }

  public fetchSharedDocCountDetail(): Observable<ResponseModel> {
    const docUrl = `${this._apiUrl}documentshare/stats`
    return this.http.get<ResponseModel>(docUrl)
  }
}
