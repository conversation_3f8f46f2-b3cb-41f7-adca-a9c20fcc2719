import {
  ChangeDetectionStrategy,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule, RadioButtonModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TiffPaperSizes } from '../../models/print-summary.models'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { FormGroup, ReactiveFormsModule, Validators } from '@angular/forms'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { Subject, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-print-resize-image',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    LabelModule,
    DropDownListModule,
    RadioButtonModule,
    ReactiveFormsModule,
  ],
  templateUrl: './print-resize-image.component.html',
  styleUrl: './print-resize-image.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrintResizeImageComponent implements OnInit, OnDestroy {
  private printDocumentFormService = inject(PrintDocumentFormService)

  private toDestory$ = new Subject<void>()
  public get imageResizeOption(): FormGroup {
    return this.printDocumentFormService.printForm.get(
      'imageResizeOption'
    ) as FormGroup
  }
  private paperSizeDimensions = {
    [TiffPaperSizes.LETTER]: { width: 8.5, height: 11 },
    [TiffPaperSizes.A3]: { width: 11.69, height: 16.54 },
    [TiffPaperSizes.A4]: { width: 8.27, height: 11.69 },
    [TiffPaperSizes.LEGAL]: { width: 8.5, height: 14 },
    [TiffPaperSizes.LEDGER]: { width: 17, height: 11 },
    [TiffPaperSizes.TABLOID]: { width: 11, height: 17 },
  }

  public tiffPaperSizesLabel = TiffPaperSizes
  public tiffPaperSizesList = Object.entries(TiffPaperSizes)
    .filter(([key]) => !isNaN(Number(key)))
    .map(([key, value]) => ({
      text: value,
      value: TiffPaperSizes[value],
    }))

  private SELECTED_DPI = 300

  public ngOnInit(): void {
    this.setupFormSubscriptions()
  }
  private setupFormSubscriptions(): void {
    // Handle resize image checkbox changes
    this.imageResizeOption
      .get('resizeImage')
      ?.valueChanges.pipe(takeUntil(this.toDestory$))
      .subscribe((checked) => {
        if (checked) {
          this.enableAllControls()
        } else {
          this.disableAllControls()
        }
      })

    this.imageResizeOption
      .get('dimension')
      ?.valueChanges.pipe(takeUntil(this.toDestory$))
      .subscribe((dimension) => {
        const widthControl = this.imageResizeOption.get('width')
        const heightControl = this.imageResizeOption.get('height')
        const maintainAspectRatioControl = this.imageResizeOption.get(
          'maintainAspectRatio'
        )

        if (dimension === 'WIDTH') {
          widthControl?.enable()
          heightControl?.disable()
          maintainAspectRatioControl?.enable()
        } else if (dimension === 'HEIGHT') {
          widthControl?.disable()
          heightControl?.enable()
          maintainAspectRatioControl?.enable()
        } else {
          // BOTH
          widthControl?.enable()
          heightControl?.enable()
          maintainAspectRatioControl?.disable()
          maintainAspectRatioControl?.setValue(false)
        }
        // Update validators based on dimension selection
        this.updateDimensionValidators(dimension)
      })

    // Handle paper size changes
    this.imageResizeOption
      .get('paperSize')
      ?.valueChanges.pipe(takeUntil(this.toDestory$))
      .subscribe((paperSize) => {
        this.handlePaperSizeChange(paperSize)

        // Disable resize dimension when not custom size
        const resizeDimensionControl = this.imageResizeOption.get('dimension')
        const widthControl = this.imageResizeOption.get('width')
        const heightControl = this.imageResizeOption.get('height')
        if (paperSize !== TiffPaperSizes.CUSTOM_SIZE) {
          resizeDimensionControl?.disable()
          resizeDimensionControl?.setValue('BOTH')
          widthControl?.disable()
          heightControl?.disable()
          this.imageResizeOption.get('maintainAspectRatio')?.disable()
          this.clearDimensionValidators()
        } else {
          widthControl?.enable()
          heightControl?.enable()
          resizeDimensionControl?.enable()
          this.updateDimensionValidators(resizeDimensionControl?.value)
        }
      })

    // Handle size unit changes
    this.imageResizeOption
      .get('sizeUnit')
      ?.valueChanges.pipe(takeUntil(this.toDestory$))
      .subscribe(() => {
        const paperSize = this.imageResizeOption.get('paperSize')?.value
        if (paperSize !== TiffPaperSizes.CUSTOM_SIZE) {
          this.updateDimensions(paperSize)
        }
      })

    this.imageResizeOption
      .get('maintainAspectRatio')
      ?.valueChanges.pipe(takeUntil(this.toDestory$))
      .subscribe((maintainRatio) => {
        const widthControl = this.imageResizeOption.get('width')
        const heightControl = this.imageResizeOption.get('height')
        const dimension = this.imageResizeOption.get('dimension')?.value

        if (dimension === 'WIDTH') {
          heightControl?.disable()
        } else if (dimension === 'HEIGHT') {
          widthControl?.disable()
        }
      })
  }
  private enableAllControls(): void {
    Object.keys(this.imageResizeOption.controls).forEach((key) => {
      if (key !== 'resizeImage') {
        const control = this.imageResizeOption.get(key)
        control?.enable()

        // Keep width and height disabled for predefined paper sizes
        if (
          (key === 'width' || key === 'height') &&
          this.imageResizeOption.get('paperSize')?.value !==
            TiffPaperSizes.CUSTOM_SIZE
        ) {
          control?.disable()
        }
      }
    })
  }

  private disableAllControls(): void {
    Object.keys(this.imageResizeOption.controls).forEach((key) => {
      if (key !== 'resizeImage') {
        this.imageResizeOption.get(key)?.disable()
      }
    })
  }

  private handlePaperSizeChange(paperSize: TiffPaperSizes): void {
    const widthControl = this.imageResizeOption.get('width')
    const heightControl = this.imageResizeOption.get('height')

    if (paperSize === TiffPaperSizes.CUSTOM_SIZE) {
      widthControl?.enable()
      heightControl?.enable()
      widthControl?.setValue('')
      heightControl?.setValue('')
    } else {
      widthControl?.disable()
      heightControl?.disable()
      this.updateDimensions(paperSize)
    }
  }

  private updateDimensions(paperSize: TiffPaperSizes): void {
    if (paperSize === TiffPaperSizes.CUSTOM_SIZE) return

    const dimensions = this.paperSizeDimensions[paperSize]
    const isInches = this.imageResizeOption.get('sizeUnit')?.value === 'INCH'
    const multiplier = isInches ? 1 : this.SELECTED_DPI

    this.imageResizeOption.patchValue({
      width: dimensions.width * multiplier,
      height: dimensions.height * multiplier,
    })
  }

  private updateDimensionValidators(dimension: string): void {
    const widthControl = this.imageResizeOption.get('width')
    const heightControl = this.imageResizeOption.get('height')
    const paperSize = this.imageResizeOption.get('paperSize')?.value

    // Clear existing validators
    this.clearDimensionValidators()

    // Only add validators if custom size is selected
    if (paperSize === TiffPaperSizes.CUSTOM_SIZE) {
      switch (dimension) {
        case 'WIDTH':
          widthControl?.setValidators([Validators.required])
          break
        case 'HEIGHT':
          heightControl?.setValidators([Validators.required])
          break
        case 'BOTH':
          widthControl?.setValidators([Validators.required])
          heightControl?.setValidators([Validators.required])
          break
      }
    }

    // Update validity
    widthControl?.updateValueAndValidity()
    heightControl?.updateValueAndValidity()
  }

  private clearDimensionValidators(): void {
    const widthControl = this.imageResizeOption.get('width')
    const heightControl = this.imageResizeOption.get('height')

    widthControl?.clearValidators()
    heightControl?.clearValidators()

    widthControl?.updateValueAndValidity()
    heightControl?.updateValueAndValidity()
  }

  public ngOnDestroy(): void {
    this.toDestory$.next()
    this.toDestory$.complete()
  }
}
