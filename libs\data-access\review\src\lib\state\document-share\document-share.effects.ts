import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { DocumentShareService } from '../../services'
import * as DocumentShareActions from './document-share.actions'
import { fetch } from '@ngrx/router-store/data-persistence'
import { CaseConvertorService } from '@venio/util/utilities'
import { HttpErrorResponse } from '@angular/common/http'
import {
  DocumentShareUserListModel,
  SharedDocRequestType,
  SharedDocumentDetailModel,
  SharedDocumentDetailRequestInfo,
} from '../../models/interfaces/document-share.model'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { from, map, Observable, switchMap, take } from 'rxjs'
import { Action, select, Store } from '@ngrx/store'
import { DocumentShareState } from './document-share.reducer'
import * as DocumentShareSelectors from './document-share.selectors'

@Injectable()
export class DocumentShareEffects {
  private readonly store = inject(Store<DocumentShareState>)

  public fetchDocumentShareUsersInternal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.fetchDocumentShareUsersInternal),
      fetch({
        run: ({ projectId }) => {
          return this.documentShareService
            .fetchDocumentShareUsersInternal(projectId)
            .pipe(
              switchMap((response: DocumentShareUserListModel) =>
                new CaseConvertorService()
                  .convertToCase<DocumentShareUserListModel>(
                    response,
                    'camelCase'
                  )
                  .then((convertedData) =>
                    DocumentShareActions.setDocumentShareUsersInternal({
                      userModels: convertedData.shareUserInfoList,
                    })
                  )
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.setUserMessage({
            messageModel: {
              message: `Error fetching internal document share users: ${error.message}`,
              success: false,
            },
          })
        },
      })
    )
  )

  public fetchDocumentShareUsersExternal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.fetchDocumentShareUsersExternal),
      fetch({
        run: ({ projectId }) => {
          return this.documentShareService
            .fetchDocumentShareUsersExternal(projectId)
            .pipe(
              switchMap((response: DocumentShareUserListModel) =>
                new CaseConvertorService()
                  .convertToCase<DocumentShareUserListModel>(
                    response,
                    'camelCase'
                  )
                  .then((convertedData) =>
                    DocumentShareActions.setDocumentShareUsersExternal({
                      userModels: convertedData.shareExtUserInfoList,
                    })
                  )
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.setUserMessage({
            messageModel: {
              message: `Error fetching external document share users: ${error.message}`,
              success: false,
            },
          })
        },
      })
    )
  )

  public shareDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.shareDocuments),
      fetch({
        run: ({ projectId, requestModel }) => {
          return from(
            new CaseConvertorService().convertToCase(requestModel, 'PascalCase')
          ).pipe(
            switchMap((convertedRequestModel) =>
              this.documentShareService
                .shareDocument(projectId, convertedRequestModel)
                .pipe(
                  map((response: string) => {
                    if (response.toUpperCase() === 'SUCCESS') {
                      return DocumentShareActions.setUserMessage({
                        messageModel: {
                          message: 'Documents shared successfully.',
                          success: true,
                        },
                      })
                    }
                    throw new Error(response)
                  })
                )
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.setUserMessage({
            messageModel: {
              message: `Error sharing documents: ${error.message}`,
              success: false,
            },
          })
        },
      })
    )
  )

  public fetchSharedDocumentDetail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.fetchSharedDocumentDetail),
      fetch({
        run: ({ projectId, docShareId }) => {
          return this.documentShareService
            .fetchSharedDocDetail(projectId, docShareId)
            .pipe(
              map((response: ResponseModel) =>
                DocumentShareActions.fetchSharedDocumentDetailSuccess({
                  sharedDocumentDetailSuccessResponse: response,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.fetchSharedDocumentDetailFailure({
            sharedDocumentDetailErrorResponse: error.error,
          })
        },
      })
    )
  )

  public fetchSharedDocumentCountDetail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.fetchSharedDocumentCountDetail),
      fetch({
        run: () => {
          return this.documentShareService.fetchSharedDocCountDetail().pipe(
            map((response: ResponseModel) =>
              DocumentShareActions.fetchSharedDocumentCountDetailSuccess({
                sharedDocumentCountDetailSuccessResponse: response,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.fetchSharedDocumentCountDetailError({
            sharedDocumentCountDetailErrorResponse: error.error,
          })
        },
      })
    )
  )

  public updateExpiryDate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.updateExpiryDate),
      fetch({
        run: ({ projectId, docShareId, payload }) => {
          return this.documentShareService
            .updateDocumentShareExpiryDate(projectId, docShareId, payload)
            .pipe(
              map((response: ResponseModel) =>
                DocumentShareActions.updateExpiryDateSuccess({
                  updatedDocumentResponse: response,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.updateExpiryDateFailure({
            errorResponse: error.error,
          })
        },
      })
    )
  )

  public unShareDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.unShareDocument),
      fetch({
        run: ({ projectId, docShareId, payload }) => {
          return this.documentShareService
            .unshareDocumentShare(projectId, docShareId, payload)
            .pipe(
              map((response: ResponseModel) =>
                DocumentShareActions.unShareDocumentSuccess({
                  unsharedDocumentResponse: response,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          return DocumentShareActions.unShareDocumentFailure({
            errorResponse: error.error,
          })
        },
      })
    )
  )

  public fetchSharedDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.fetchSharedDocuments),
      fetch({
        run: ({ projectId, filterQuery }) =>
          this.fetchSharedDocumentsHandler(projectId, filterQuery),
        onError: (action, error: HttpErrorResponse): Action =>
          this.handleFetchSharedDocumentsError(error),
      })
    )
  )

  /**
   * Handles the logic for fetching shared documents.
   * @param projectId - The project ID for which to fetch documents.
   * @param filterQuery - The filter query to apply to the request.
   * @returns An observable emitting the fetch success action.
   */
  private fetchSharedDocumentsHandler(
    projectId: number,
    filterQuery: SharedDocRequestType
  ): Observable<Action> {
    return this.store.pipe(
      select(
        DocumentShareSelectors.getStateOfDocumentShare(
          'sharedDocumentRequestInfo'
        )
      ),
      take(1),
      switchMap((existingRequestInfo: SharedDocumentDetailRequestInfo) =>
        this.documentShareService
          .fetchSharedDocList(projectId, filterQuery, existingRequestInfo)
          .pipe(
            switchMap((response: SharedDocumentDetailModel[]) =>
              this.convertResponseToCamelCase(response).then(
                (convertedResponse) =>
                  DocumentShareActions.fetchSharedDocumentsSuccess({
                    sharedDocumentsSuccessResponse: convertedResponse,
                  })
              )
            )
          )
      )
    )
  }

  public fetchSharedDocumentAvailability$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentShareActions.fetchSharedDocumentAvailability),
      fetch({
        run: ({ projectId }) => {
          return this.documentShareService
            .fetchSharedDocumentAvailability(projectId)
            .pipe(
              map((response: ResponseModel) =>
                DocumentShareActions.setSharedDocumentAvailability({
                  documentShareAvailable: response?.data === true,
                })
              )
            )
        },
      })
    )
  )

  /**
   * Converts the response to camel case.
   * @param response - The API response to be converted.
   * @returns A promise resolving to the converted response.
   */
  private async convertResponseToCamelCase(
    response: SharedDocumentDetailModel[]
  ): Promise<SharedDocumentDetailModel[]> {
    return new CaseConvertorService().convertToCase<
      SharedDocumentDetailModel[]
    >(response, 'camelCase')
  }

  /**
   * Handles the error scenario for fetching shared documents.
   * @param error - The error response.
   * @returns The failure action with the error response.
   */
  private handleFetchSharedDocumentsError(error: HttpErrorResponse): Action {
    return DocumentShareActions.fetchSharedDocumentsFailure({
      sharedDocumentsErrorResponse: error.error,
    })
  }

  constructor(
    private readonly actions$: Actions,
    private documentShareService: DocumentShareService
  ) {}
}
