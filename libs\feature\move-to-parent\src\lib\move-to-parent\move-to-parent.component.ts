import { CommonModule } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogRef,
  DialogContentBase,
  DialogModule,
  DialogsModule,
  DialogService,
  DialogCloseResult,
} from '@progress/kendo-angular-dialog'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  NumericTextBoxModule,
  TextAreaModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ExpansionPanelModule } from '@progress/kendo-angular-layout'
import {
  MoveToParentFacade,
  MoveToParentStatus,
  ReviewParamService,
} from '@venio/data-access/review'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { UuidGenerator } from '@venio/util/uuid'
import { filter, Subject, take, takeUntil, tap } from 'rxjs'

@Component({
  selector: 'venio-move-to-parent',
  standalone: true,
  imports: [
    CommonModule,
    LabelModule,
    ButtonsModule,
    NumericTextBoxModule,
    DialogModule,
    DialogsModule,
    TextAreaModule,
    LoaderModule,
    ExpansionPanelModule,
  ],
  templateUrl: './move-to-parent.component.html',
  styleUrl: './move-to-parent.component.scss',
})
export class MoveToParentComponent
  extends DialogContentBase
  implements OnDestroy, OnInit, AfterViewInit
{
  public isLoading = signal<boolean>(true)

  public destinationParentFileId: number

  public totalParentSelectedToMove = signal<number>(0)

  public isRefreshNeeded = false

  private projectId: number

  public moveProgressStatus = signal<string>('')

  private readonly toDestroy$ = new Subject<void>()

  private sessionId: string

  public fetchStatusInterval: any

  public isMoveToParentInProgress = signal<boolean>(false)

  public disableMoveButton = signal<boolean>(false)

  private readonly confirmDialogTitle = 'Confirm Move'

  private readonly confirmMessage = `Are you sure you want to move documents?`

  constructor(
    public dialog: DialogRef,
    private moveToParentFacade: MoveToParentFacade,
    private reviewParamService: ReviewParamService,
    private cdr: ChangeDetectorRef,
    private dialogService: DialogService
  ) {
    super(dialog)
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    clearInterval(this.fetchStatusInterval)
    this.moveToParentFacade.resetMoveToParentOption()
  }

  public ngOnInit(): void {
    this.reviewParamService.projectId.pipe(take(1)).subscribe((projectId) => {
      this.projectId = projectId
    })
    this.moveToParentFacade.getMoveToParentFileCount$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((count: number) => {
        this.totalParentSelectedToMove.set(count)
        this.isLoading.set(false)
        this.cdr.detectChanges()
      })
    this.moveToParentFacade.fetchSelectedParentCountForMoveToParent(
      this.projectId
    )

    this.moveToParentFacade.getMoveToParentStatus$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((status: MoveToParentStatus[]) => {
        if (status) {
          const isCompleted = status.some((m) => m.isCompleted === true)
          this.isMoveToParentInProgress.set(!isCompleted)
          this.disableMoveButton.set(isCompleted)
          const progressStatus = status.map((m) => m.statusMessage).join('\n')
          this.moveProgressStatus.set(progressStatus)
        }
      })
    this.moveToParentFacade.getMoveToParentFailed$
      .pipe(
        filter((m) => !!m),
        tap(() => {
          this.moveToParentFacade.clearMoveToParentFailedStatus()
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.isMoveToParentInProgress.set(false)
        this.disableMoveButton.set(false)
      })

    this.moveToParentFacade.getMoveToParentSuccess$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((message: string) => {
        if (message) {
          this.isRefreshNeeded = true

          this.fetchStatusInterval = setInterval(() => {
            if (this.isMoveToParentInProgress()) {
              this.moveToParentFacade.fetchMoveToParentStatus(
                this.projectId,
                this.sessionId
              )
            } else {
              clearInterval(this.fetchStatusInterval)
            }
          }, 1000)
        }
      })
  }

  public onCancelAction(): void {
    const result = {
      isRefreshNeeded: this.isRefreshNeeded,
    }
    this.dialog.close(result)
  }

  public moveClicked(): void {
    const dialogRef = this.openConfirmDialog()
    dialogRef.result
      .pipe(
        filter((result) => !!result && !(result instanceof DialogCloseResult)),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.moveToParent()
      })
  }

  private openConfirmDialog(): DialogRef {
    const dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
    })
    dialogRef.content.instance.message = this.confirmMessage
    dialogRef.content.instance.title = this.confirmDialogTitle

    return dialogRef
  }

  public moveToParent(): void {
    this.isMoveToParentInProgress.set(true)
    this.disableMoveButton.set(true)
    this.sessionId = UuidGenerator.uuid.toString().replace(/-/g, '_')
    this.moveToParentFacade.setSessionId(this.sessionId)

    this.moveToParentFacade.moveToParentDocument(
      this.projectId,
      this.destinationParentFileId,
      this.sessionId
    )
  }

  public enableMoveOption(): boolean {
    return (
      (this.totalParentSelectedToMove() || 0) > 0 &&
      (this.destinationParentFileId || 0) > 0 &&
      this.isMoveToParentInProgress() === false &&
      this.disableMoveButton() === false
    )
  }

  public onParentIdChanged(e): void {
    this.destinationParentFileId = e
    return
  }
}
