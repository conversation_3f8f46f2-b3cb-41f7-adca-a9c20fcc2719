import {
  ComponentFix<PERSON>,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { PrintResizeImageComponent } from './print-resize-image.component'
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'

import { CommonModule } from '@angular/common'
import { InputsModule, RadioButtonModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { LOCALE_ID } from '@angular/core'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { PrintDocumentFormService } from '../../services/print-document-form.service'
import { TiffPaperSizes } from '../../models/print-summary.models'

describe('PrintResizeImageComponent', () => {
  let component: PrintResizeImageComponent
  let fixture: ComponentFixture<PrintResizeImageComponent>
  let mockPrintDocumentFormService: Partial<PrintDocumentFormService>
  let mockFormGroup: FormGroup

  beforeEach(async () => {
    // Create a mock form group that mimics the structure of imageResizeOption
    const formBuilder = new FormBuilder()
    mockFormGroup = formBuilder.group({
      resizeImage: [false],
      paperSize: [{ value: 0, disabled: true }],
      dimension: [{ value: 'BOTH', disabled: true }],
      width: [{ value: 8.5, disabled: true }],
      height: [{ value: 11, disabled: true }],
      sizeUnit: [{ value: 'INCH', disabled: true }],
      maintainAspectRatio: [{ value: false, disabled: true }],
    })

    // Create a mock PrintDocumentFormService
    mockPrintDocumentFormService = {
      printForm: formBuilder.group({
        imageResizeOption: mockFormGroup,
      }),
    }

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        ReactiveFormsModule,
        InputsModule,
        LabelModule,
        DropDownListModule,
        RadioButtonModule,
        NoopAnimationsModule,
        PrintResizeImageComponent,
      ],
      providers: [
        {
          provide: PrintDocumentFormService,
          useValue: mockPrintDocumentFormService,
        },
        FormBuilder,
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: LOCALE_ID, useValue: 'en-US' },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(PrintResizeImageComponent)
    component = fixture.componentInstance

    // Initialize the form service
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should have a valid imageResizeOption FormGroup', () => {
    expect(component.imageResizeOption).toBeDefined()
    expect(component.imageResizeOption instanceof FormGroup).toBeTruthy()
  })

  it('should have tiffPaperSizesList populated with correct values', () => {
    expect(component.tiffPaperSizesList).toBeDefined()
    expect(component.tiffPaperSizesList).toHaveLength(7) // 7 paper sizes including CUSTOM_SIZE

    // Check that the list contains the expected paper sizes
    const paperSizes = component.tiffPaperSizesList.map((item) => item.value)
    expect(paperSizes).toContain(TiffPaperSizes.LETTER)
    expect(paperSizes).toContain(TiffPaperSizes.A3)
    expect(paperSizes).toContain(TiffPaperSizes.A4)
    expect(paperSizes).toContain(TiffPaperSizes.LEGAL)
    expect(paperSizes).toContain(TiffPaperSizes.LEDGER)
    expect(paperSizes).toContain(TiffPaperSizes.TABLOID)
    expect(paperSizes).toContain(TiffPaperSizes.CUSTOM_SIZE)
  })

  it('should enable all controls when resizeImage is checked', fakeAsync(() => {
    // GIVEN the resizeImage checkbox is initially unchecked
    expect(component.imageResizeOption.get('resizeImage')?.value).toBe(false)

    // WHEN the resizeImage checkbox is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // THEN all other controls should be enabled (except width/height for non-custom sizes)
    expect(component.imageResizeOption.get('paperSize')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('sizeUnit')?.enabled).toBe(true)

    // Width and height should still be disabled for predefined paper sizes
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
  }))

  it('should disable all controls when resizeImage is unchecked', fakeAsync(() => {
    // GIVEN the resizeImage checkbox is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // WHEN the resizeImage checkbox is unchecked
    component.imageResizeOption.get('resizeImage')?.setValue(false)
    tick() // Process the subscription

    // THEN all other controls should be disabled
    expect(component.imageResizeOption.get('paperSize')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('dimension')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('sizeUnit')?.enabled).toBe(false)
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
  }))

  it('should handle paper size change to CUSTOM_SIZE correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // WHEN paper size is changed to CUSTOM_SIZE
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // THEN width and height controls should be enabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)

    // AND dimension control should be enabled
    expect(component.imageResizeOption.get('dimension')?.enabled).toBe(true)
  }))

  it('should update dimensions when paper size changes to a predefined size', fakeAsync(() => {
    // GIVEN resizeImage is checked
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription

    // AND sizeUnit is set to INCH
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // WHEN paper size is changed to LETTER
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // THEN width and height should be updated with the correct dimensions
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(11)
  }))

  it('should handle dimension change to WIDTH correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to WIDTH
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // THEN width control should be enabled and height control should be disabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)

    // AND maintainAspectRatio should be enabled
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(true)
  }))

  it('should handle dimension change to HEIGHT correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to HEIGHT
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // THEN height control should be enabled and width control should be disabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(false)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)

    // AND maintainAspectRatio should be enabled
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(true)
  }))

  it('should handle dimension change to BOTH correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to BOTH
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // THEN both width and height controls should be enabled
    expect(component.imageResizeOption.get('width')?.enabled).toBe(true)
    expect(component.imageResizeOption.get('height')?.enabled).toBe(true)

    // AND maintainAspectRatio should be disabled and set to false
    expect(
      component.imageResizeOption.get('maintainAspectRatio')?.enabled
    ).toBe(false)
    expect(component.imageResizeOption.get('maintainAspectRatio')?.value).toBe(
      false
    )
  }))

  it('should update validators when dimension changes for CUSTOM_SIZE', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to WIDTH
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // THEN width should have required validator
    expect(component.imageResizeOption.get('width')?.hasValidator).toBeTruthy()

    // AND width should be invalid when empty
    component.imageResizeOption.get('width')?.setValue('')
    expect(component.imageResizeOption.get('width')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('width')?.errors?.['required']
    ).toBeTruthy()

    // Check if height has required validator (it might have other validators)
    const heightControl = component.imageResizeOption.get('height')
    const hasRequiredValidator = heightControl?.hasValidator(
      Validators.required
    )
    expect(hasRequiredValidator).toBeFalsy()
  }))

  it('should update dimensions when size unit changes', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is LETTER
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // AND sizeUnit is INCH
    component.imageResizeOption.get('sizeUnit')?.setValue('INCH')
    tick() // Process the subscription

    // Initial values for LETTER in inches
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5)
    expect(component.imageResizeOption.get('height')?.value).toBe(11)

    // WHEN sizeUnit is changed to PIXEL
    component.imageResizeOption.get('sizeUnit')?.setValue('PIXEL')
    tick() // Process the subscription

    // THEN dimensions should be updated (multiplied by 300 DPI)
    expect(component.imageResizeOption.get('width')?.value).toBe(8.5 * 300)
    expect(component.imageResizeOption.get('height')?.value).toBe(11 * 300)
  }))

  it('should clean up subscriptions on destroy', () => {
    // GIVEN the component is initialized

    // WHEN ngOnDestroy is called
    const nextSpy = jest.spyOn(component['toDestory$'], 'next')
    const completeSpy = jest.spyOn(component['toDestory$'], 'complete')

    component.ngOnDestroy()

    // THEN the subject should be completed
    expect(nextSpy).toHaveBeenCalled()
    expect(completeSpy).toHaveBeenCalled()
  })

  it('should update validators for HEIGHT dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to HEIGHT
    component.imageResizeOption.get('dimension')?.setValue('HEIGHT')
    tick() // Process the subscription

    // THEN height should have required validator
    const heightControl = component.imageResizeOption.get('height')
    const hasRequiredValidator = heightControl?.hasValidator(
      Validators.required
    )
    expect(hasRequiredValidator).toBeTruthy()

    // AND height should be invalid when empty
    component.imageResizeOption.get('height')?.setValue('')
    expect(component.imageResizeOption.get('height')?.valid).toBe(false)
    expect(
      component.imageResizeOption.get('height')?.errors?.['required']
    ).toBeTruthy()

    // AND width should not have required validator
    const widthControl = component.imageResizeOption.get('width')
    const widthHasRequiredValidator = widthControl?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidator).toBeFalsy()
  }))

  it('should update validators for BOTH dimension', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription

    // WHEN dimension is changed to BOTH
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // THEN both width and height should have required validator
    const widthControl = component.imageResizeOption.get('width')
    const widthHasRequiredValidator = widthControl?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidator).toBeTruthy()

    const heightControl = component.imageResizeOption.get('height')
    const heightHasRequiredValidator = heightControl?.hasValidator(
      Validators.required
    )
    expect(heightHasRequiredValidator).toBeTruthy()

    // AND both should be invalid when empty
    component.imageResizeOption.get('width')?.setValue('')
    component.imageResizeOption.get('height')?.setValue('')
    expect(component.imageResizeOption.get('width')?.valid).toBe(false)
    expect(component.imageResizeOption.get('height')?.valid).toBe(false)
  }))

  it('should clear validators when paper size changes from CUSTOM_SIZE to predefined size', fakeAsync(() => {
    // GIVEN resizeImage is checked and paper size is CUSTOM_SIZE with validators
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('BOTH')
    tick() // Process the subscription

    // Verify validators are set
    const widthControl = component.imageResizeOption.get('width')
    const widthHasRequiredValidator = widthControl?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidator).toBeTruthy()

    // WHEN paper size is changed to a predefined size
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.LETTER)
    tick() // Process the subscription

    // THEN validators should be cleared
    const widthControlAfter = component.imageResizeOption.get('width')
    const widthHasRequiredValidatorAfter = widthControlAfter?.hasValidator(
      Validators.required
    )
    expect(widthHasRequiredValidatorAfter).toBeFalsy()
  }))

  it('should handle maintainAspectRatio changes correctly', fakeAsync(() => {
    // GIVEN resizeImage is checked and dimension is WIDTH
    component.imageResizeOption.get('resizeImage')?.setValue(true)
    tick() // Process the subscription
    component.imageResizeOption
      .get('paperSize')
      ?.setValue(TiffPaperSizes.CUSTOM_SIZE)
    tick() // Process the subscription
    component.imageResizeOption.get('dimension')?.setValue('WIDTH')
    tick() // Process the subscription

    // WHEN maintainAspectRatio is toggled
    component.imageResizeOption.get('maintainAspectRatio')?.setValue(true)
    tick() // Process the subscription

    // THEN height should be disabled
    expect(component.imageResizeOption.get('height')?.enabled).toBe(false)
  }))
})
