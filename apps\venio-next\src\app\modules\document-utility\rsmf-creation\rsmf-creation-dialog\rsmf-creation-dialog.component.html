<div class="v-rsmf-creation-container">
  <kendo-dialog-titlebar (close)="close('cancel')">
    <div class="t-flex t-justify-between t-w-full t-items-center">
      <div class="t-w-[14%] t-px-2">
        <div class="t-block">{{ dialogTitle }}</div>
      </div>
      <div class="t-w-[72%] t-t-px-2">
        <div class="t-block">
          <div
            #appendNotification
            class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-14 t-w-full"></div>
        </div>
      </div>
      <div class="t-w-[14%] t-px-2"></div>
    </div>
  </kendo-dialog-titlebar>

  <kendo-tabstrip (tabSelect)="onSelect($event)">
    <kendo-tabstrip-tab
      title="Convert RSMF"
      *ngIf="showCreateRSMFTab"
      [selected]="isSelected(0)">
      <ng-template kendoTabContent>
        <div class="t-flex t-flex-col t-w-full t-mt-4" *ngIf="showMappingGrid">
          <kendo-dropdownlist
            class="t-w-1/4"
            [data]="source"
            [valuePrimitive]="true"
            [defaultItem]="defaultSouceValue"
            textField="displayName"
            valueField="value"
            [itemDisabled]="itemDisabled"
            (valueChange)="onSourceChange($event)"
            [(ngModel)]="selectedSource"></kendo-dropdownlist>
          <div class="t-flex t-flex-col t-w-full t-mt-3">
            <kendo-grid
              class="t-flex t-flex-col-reverse"
              [data]="gridDataRSMF"
              [resizable]="true">
              <kendo-grid-column
                field="venioField"
                title="Source Fields"
                headerClass="t-text-primary"
                [width]="columnWidth"
                [minResizableWidth]="130">
                <ng-template kendoGridHeaderTemplate>
                  <span title="Source Fields">Source Fields</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span title="{{ dataItem.venioField }}">{{
                    dataItem.venioField
                  }}</span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="rsmfField"
                headerClass="t-text-primary"
                [width]="columnWidth"
                title="RSMF Fields">
                <ng-template kendoGridHeaderTemplate>
                  <span title="RSMF Fields">RSMF Fields</span>
                </ng-template>
                <ng-template
                  kendoGridCellTemplate
                  let-dataItem
                  let-rowIndex="rowIndex">
                  <kendo-dropdownlist
                    class="t-w-2/5"
                    [data]="filteredVenioFields[dataItem.venioField]"
                    textField="text"
                    valueField="value"
                    [valuePrimitive]="true"
                    [value]="dataItem.rsmfField"
                    [defaultItem]="{
                      text: 'Search/Select Venio Field',
                      value: null
                    }"
                    (valueChange)="onVenioFieldChange(rowIndex, $event)"
                    (filterChange)="
                      handleFilterChange($event, dataItem.venioField)
                    "
                    [filterable]="true"
                    [itemDisabled]="itemDisabled">
                    <ng-template kendoDropDownListItemTemplate let-dataItem>
                      <span
                        [ngClass]="{
                          'v-disabled-item': dataItem.value === null
                        }">
                        {{ dataItem.text }}
                      </span>
                    </ng-template>
                  </kendo-dropdownlist>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>
          </div>
        </div>
        <div class="t-flex t-flex-col t-w-full t-mt-3" *ngIf="!showMappingGrid">
          <div class="t-flex t-flex-row mt-3">
            <span class="t-font-medium t-text-[12px] t-text-[#707070]"
              >Selected Source -
            </span>
            <span class="t-font-extrabold t-text-[12px] t-text-[#000000]"
              >&nbsp;{{ selectedSource | titlecase }}</span
            >
          </div>
          <div
            class="t-flex t-w-[20vw] t-flex-col t-h-[30vh] t-justify-center t-self-center"
            *ngIf="showConversionProgress">
            <span
              class="t-font-bold t-text-[12px] t-tracking-[0.48px] t-text-[#5E6366] my-5"
              >Conversion InProgress</span
            >
            <kendo-progressbar
              [animation]="true"
              kendoTooltip
              title="Conversion Progress"
              themecolor="primary"
              class="v-custom-progress-bar t-h-[12px] t-w-full"
              [min]="0"
              [max]="100"
              [value]="conversionCompletion"
              [label]="label">
            </kendo-progressbar>
          </div>

          <div
            class="t-flex t-w-[30vw] t-h-[30vh] t-flex-col t-justify-center t-self-center t-text-center t-gap-4"
            *ngIf="showConversionCompleted">
            <h2 class="t-font-medium t-tracking-[0.48px] t-text-[#5E6366]">
              Conversion Completed
            </h2>
            <h4 class="t-font-medium t-tracking-[0.48px] t-text-[#5E6366] my-2">
              Click to download the RSMF zip file
            </h4>
            <div
              class="t-self-center t-cursor-pointer"
              venioSvgLoader
              hoverColor="#FFFFFF"
              [color]="'#40C0D1'"
              svgUrl="assets/svg/icon-material-round-sim-card-download.svg"
              height="40px"
              width="34px"
              [ngStyle]="{
                'pointer-events': disableDownloadArchiveBtn ? 'none' : 'auto',
                opacity: disableDownloadArchiveBtn ? '0.5' : '1'
              }"
              [class.disabled]="disableDownloadArchiveBtn"
              (click)="downloadRSMFArchive()"></div>
          </div>
        </div>
      </ng-template>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab title="RSMF Status" [selected]="isSelected(1)">
      <ng-template kendoTabContent>
        <kendo-grid
          class="t-flex t-flex-col-reverse v-custom-grid"
          [loading]="(isDeletingRSMF | async) || (isRSMFStatusLoading | async)"
          [data]="rsmfStatusList.data"
          [pageSize]="pageSizeStatus"
          [skip]="skipStatus"
          [pageable]="true"
          [resizable]="true">
          <ng-template kendoPagerTemplate>
            <div
              class="t-flex t-w-full t-justify-end t-items-center"
              kendoTooltip>
              <button
                #parentRefresh
                class="!t-p-1.5 t-h-fit !t-border-[#9AD3A6] t-leading-[0.8] hover:!t-bg-[#9AD3A6]"
                kendoButton
                size="none"
                fillMode="outline"
                (click)="onRefreshClick()"
                title="Refresh">
                <span
                  [parentElement]="parentRefresh.element"
                  venioSvgLoader
                  svgUrl="assets/svg/refresh.svg"
                  color="#9AD3A6"
                  hoverColor="#FFFFFF"
                  height="1rem"
                  width="1rem"></span>
              </button>
              <kendo-grid-spacer></kendo-grid-spacer>
              <venio-pagination
                [currentPage]="currentPageStatus"
                [disabled]="rsmfStatusList?.data?.length === 0"
                [totalRecords]="rsmfStatusList?.total"
                [pageSize]="pageSizeStatus"
                [showPageJumper]="false"
                [showPageSize]="true"
                [showRowNumberInputBox]="true"
                (pageChanged)="pageChanged($event)"
                (pageSizeChanged)="pageSizeChanged($event)"
                class="t-px-5 t-block t-py-2">
              </venio-pagination>
            </div>
          </ng-template>
          <kendo-grid-column
            field="jobId"
            title="#"
            headerClass="t-text-primary"
            [width]="30"
            [minResizableWidth]="30"></kendo-grid-column>
          <kendo-grid-column
            field="source"
            title="Source"
            headerClass="t-text-primary"
            [width]="80"
            [minResizableWidth]="80">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="Source">Source</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span
                class="t-text-[#5E6366]"
                kendoTooltip
                title="{{ dataItem.source }}"
                >{{ dataItem.source }}</span
              >
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="createdByOn"
            title="Converted By & On"
            headerClass="t-text-primary"
            [width]="150"
            [minResizableWidth]="150">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="Converted By & On"
                >Converted By & On</span
              >
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span
                kendoTooltip
                title="{{ dataItem.queuedBy }} {{
                  dataItem.queuedOn | date : 'dd MM yyyy'
                }}">
                <span class="t-text-[14px] t-font-medium t-text-[#000000]">{{
                  dataItem.queuedBy | titlecase
                }}</span>
                <span class="t-text-[14px] t-text-[#5E6366]">
                  {{ dataItem.queuedOn | date : 'dd MM yyyy' }}</span
                >
                <span class="t-text-[11px] t-font-medium t-text-[#A7A9AA]">
                  {{ dataItem.queuedOn | date : 'shortTime' }}</span
                >
              </span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="status"
            title="Status"
            headerClass="t-text-primary"
            [width]="50"
            [minResizableWidth]="50">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="Status">Status</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span
                class="t-text-[#5E6366]"
                kendoTooltip
                title="{{ statusMap[dataItem.status] }}">
                {{ statusMap[dataItem.status] }}
              </span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="action"
            title="Action"
            headerClass="t-text-primary"
            [width]="60"
            [minResizableWidth]="60">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="Action">Action</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <kendo-buttongroup *ngFor="let icon of svgIconForGridControls">
                <button
                  kendoButton
                  #actionGrid
                  *ngIf="
                    icon.actionType !== 'Download Excluded Files' ||
                    dataItem.hasExcludedFilesLog
                  "
                  (click)="browseActionClicked(icon.actionType, dataItem.jobId)"
                  fillMode="outline"
                  class="!t-p-[5px] t-w-[24px] !t-border !t-border-[#263238] !t-rounded-l-[2px] hover:!t-bg-[#263238]"
                  [ngClass]="{
                    'hover:!t-bg-[#2F3080]': icon.hoverBtnBg === '#2F3080',
                    'hover:!t-bg-[#ED7425]': icon.hoverBtnBg === '#ED7425',
                    't-opacity-50 t-cursor-not-allowed':
                      statusMap[dataItem.status] !== 'Completed'
                  }"
                  kendoTooltip
                  [title]="icon.actionType"
                  size="none"
                  [disabled]="statusMap[dataItem.status] !== 'Completed'">
                  <span
                    [parentElement]="actionGrid.element"
                    venioSvgLoader
                    [hoverColor]="icon.hoverColor"
                    [color]="icon.color"
                    [svgUrl]="icon.iconPath"
                    height="0.9rem"
                    width="1rem"></span>
                </button>
              </kendo-buttongroup>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>
      </ng-template>
    </kendo-tabstrip-tab>
  </kendo-tabstrip>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      @if(this.tabStatus === 1 || isSelected(1)){
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CLOSE
      </button>
      } @else {
      <button
        kendoButton
        class="v-custom-secondary-button"
        themeColor="secondary"
        data-qa="save-button"
        fillMode="outline"
        *ngIf="showMappingGrid"
        [disabled]="createBtnDisabled"
        (click)="createRSMFConvsersion()">
        CREATE
      </button>
      <button
        kendoButton
        fillMode="outline"
        themeColor="dark"
        data-qa="cancel-button"
        (click)="close('yes')">
        CANCEL
      </button>
      }
    </div>
  </kendo-dialog-actions>
  <div
    class="t-absolute t-top-0 t-left-0 t-w-full t-h-full t-bg-[#00000066] t-flex t-items-center t-justify-center t-z-[100]"
    *ngIf="isCreatingRSMF$ | async">
    <div class="t-flex t-flex-col t-items-center t-justify-center">
      <div class="t-flex t-items-center t-space-x-2">
        <kendo-loader [themeColor]="'light'" [type]="'pulsing'"></kendo-loader>
      </div>
      <p class="t-text-white">Queuing in progress...</p>
    </div>
  </div>
</div>
